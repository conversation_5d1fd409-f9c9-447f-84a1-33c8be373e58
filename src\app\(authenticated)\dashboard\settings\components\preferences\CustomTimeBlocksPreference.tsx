"use client"

import { useState } from 'react';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Trash2, Plus } from 'lucide-react';
import { UseFormReturn } from 'react-hook-form';
import { PreferencesFormValues } from '../../schemas';

interface CustomTimeBlocksPreferenceProps {
  form: UseFormReturn<PreferencesFormValues>;
}

export function CustomTimeBlocksPreference({ form }: CustomTimeBlocksPreferenceProps) {
  const [newStartTime, setNewStartTime] = useState('');
  const [newEndTime, setNewEndTime] = useState('');

  const customTimeBlocks = form.watch('customTimeBlocks') || [];

  const addCustomTimeBlock = () => {
    if (newStartTime && newEndTime) {
      // Validate that end time is after start time
      const startMinutes = parseInt(newStartTime.split(':')[0]) * 60 + parseInt(newStartTime.split(':')[1]);
      const endMinutes = parseInt(newEndTime.split(':')[0]) * 60 + parseInt(newEndTime.split(':')[1]);

      if (endMinutes <= startMinutes) {
        // Handle overnight blocks (e.g., 23:00 to 02:00)
        if (endMinutes + 24 * 60 <= startMinutes) {
          alert('End time must be after start time');
          return;
        }
      }

      const currentBlocks = form.getValues('customTimeBlocks') || [];
      form.setValue('customTimeBlocks', [
        ...currentBlocks,
        { startTime: newStartTime, endTime: newEndTime }
      ]);
      setNewStartTime('');
      setNewEndTime('');
    }
  };

  const removeCustomTimeBlock = (index: number) => {
    const currentBlocks = form.getValues('customTimeBlocks') || [];
    form.setValue('customTimeBlocks', currentBlocks.filter((_, i) => i !== index));
  };

  const clearAllCustomTimeBlocks = () => {
    form.setValue('customTimeBlocks', []);
  };

  return (
    <div className="bg-muted/10 p-3 rounded-md">
      <FormField
        control={form.control}
        name="customTimeBlocks"
        render={() => (
          <FormItem>
            <FormLabel className="text-sm font-medium">Custom Time Blocks</FormLabel>
            <FormDescription className="text-xs mb-3">
              Create custom time ranges for your grid view. When custom blocks are set, they completely replace the regular time intervals. Only your custom time blocks will be shown in the grid.
              <br />
              <span className="text-muted-foreground">Examples: 12:00 AM - 4:00 AM (night), 8:00 PM - 11:00 PM (evening)</span>
            </FormDescription>
            
            {/* Add new custom time block */}
            <Card className="mb-3">
              <CardContent className="p-3">
                <div className="flex gap-2 items-end">
                  <div className="flex-1">
                    <label className="text-xs text-muted-foreground">Start Time</label>
                    <Input
                      type="time"
                      value={newStartTime}
                      onChange={(e) => setNewStartTime(e.target.value)}
                      className="h-8"
                    />
                  </div>
                  <div className="flex-1">
                    <label className="text-xs text-muted-foreground">End Time</label>
                    <Input
                      type="time"
                      value={newEndTime}
                      onChange={(e) => setNewEndTime(e.target.value)}
                      className="h-8"
                    />
                  </div>
                  <Button
                    type="button"
                    onClick={addCustomTimeBlock}
                    disabled={!newStartTime || !newEndTime}
                    size="sm"
                    className="h-8"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Display existing custom time blocks */}
            {customTimeBlocks.length > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Current Custom Blocks:</span>
                  <Button
                    type="button"
                    onClick={clearAllCustomTimeBlocks}
                    variant="outline"
                    size="sm"
                    className="h-6 text-xs"
                  >
                    Clear All
                  </Button>
                </div>
                
                {customTimeBlocks.map((block, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-card rounded border"
                  >
                    <span className="text-sm">
                      {block.startTime} - {block.endTime}
                    </span>
                    <Button
                      type="button"
                      onClick={() => removeCustomTimeBlock(index)}
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            )}

            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
