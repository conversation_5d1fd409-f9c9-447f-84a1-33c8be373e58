"use client";

import { useState, useEffect, useCallback } from 'react';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  preferences?: {
    timeInterval: string;
    startHour: string;
    endHour: string;
    timeFormat: string;
    darkMode: boolean;
    syncEnabled: boolean;
    emailNotifications: boolean;
    customTimeBlocks?: Array<{ startTime: string; endTime: string }>;
  };
}

// Cache the profile data in memory
let cachedProfile = {
  data: null as UserProfile | null,
  lastFetched: 0
};

const PROFILE_CACHE_TIME = 5 * 60 * 1000; // 5 minutes

export function useProfile() {
  const [isLoading, setIsLoading] = useState(true);
  const [profile, setProfile] = useState<UserProfile | null>(cachedProfile.data);
  const [error, setError] = useState<string | null>(null);

  // Helper function to try loading profile from localStorage
  const tryLoadFromLocalStorage = useCallback(() => {
    try {
      const storedProfile = localStorage.getItem('userProfile');
      const storedUser = localStorage.getItem('user');

      if (storedProfile) {
        const parsedProfile = JSON.parse(storedProfile);
        setProfile(parsedProfile);

        // Update cache
        cachedProfile = {
          data: parsedProfile,
          lastFetched: Date.now() - (PROFILE_CACHE_TIME / 2) // Set to half expired so it refreshes soon but not immediately
        };

        return true;
      } else if (storedUser) {
        // If we have user data but not profile, create a minimal profile
        const userData = JSON.parse(storedUser);
        const minimalProfile = {
          id: userData.id,
          name: userData.name,
          email: userData.email
        };

        setProfile(minimalProfile);

        // Update cache with minimal data
        cachedProfile = {
          data: minimalProfile,
          lastFetched: Date.now() - (PROFILE_CACHE_TIME / 2)
        };

        return true;
      }
    } catch (e) {
      // Silent fail - will return false below
    }
    return false;
  }, []);

  // Function to fetch profile that can be called to refresh data
  const fetchProfile = useCallback(async (forceRefresh = false) => {
    try {
      const now = Date.now();

      // Return cached value if valid and not forcing refresh
      if (!forceRefresh && cachedProfile.data && now - cachedProfile.lastFetched < PROFILE_CACHE_TIME) {
        setProfile(cachedProfile.data);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      // Try to load from localStorage first if available and cache is expired
      if (!forceRefresh && tryLoadFromLocalStorage()) {
        setIsLoading(false);
        return;
      }

      // Fetch user profile using the cookie
      const response = await fetch('/api/auth/profile', {
        method: 'GET',
        credentials: 'include',
      });

      const data = await response.json();

      if (response.ok) {
        // Handle the correct response structure with data property
        const profileData = data.data || data;

        // Update cache
        cachedProfile = {
          data: profileData,
          lastFetched: now
        };

        setProfile(profileData);

        // Store in localStorage for offline access
        localStorage.setItem('userProfile', JSON.stringify(profileData));
      } else {
        // Handle error response
        const errorMessage = data.error?.message || data.message || 'Failed to fetch profile';
        setError(errorMessage);

        // Try to get profile from localStorage as fallback
        tryLoadFromLocalStorage();
      }
    } catch (error) {
      setError('An error occurred while fetching profile');

      // Try to get profile from localStorage as fallback
      tryLoadFromLocalStorage();
    } finally {
      setIsLoading(false);
    }
  }, [tryLoadFromLocalStorage]);

  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  return {
    isLoading,
    profile,
    error,
    refreshProfile: (forceRefresh = false) => fetchProfile(forceRefresh)
  };
}
