"use client"

import { Card } from '@/components/ui/card';
import { TimeBlock } from '@/lib/types';
import { TimeBlockGrid } from '@/components/time-blocks/time-block-grid';
import { toast } from 'sonner';

interface GridViewTabProps {
  selectedDate: Date;
  filteredBlocks: TimeBlock[];
  onAddBlock: () => void;
  onEditBlock: (block: TimeBlock) => void;
  onDeleteBlock: (id: string) => Promise<boolean>;
  refreshTimeBlocks: () => Promise<void>;
  onUpdateBlock: (id: string, data: Partial<TimeBlock>) => Promise<boolean>;
}

export function GridViewTab({
  selectedDate,
  filteredBlocks,
  onAddBlock,
  onEditBlock,
  onDeleteBlock,
  refreshTimeBlocks,
  onUpdateBlock
}: GridViewTabProps) {
  return (
    <Card className="shadow-sm border-0 overflow-hidden">
      <TimeBlockGrid
        date={selectedDate}
        timeBlocks={filteredBlocks}
        onAddBlock={onAddBlock}
        onEditBlock={onEditBlock}
        onUpdate={async (id, data) => {
          try {
            await onUpdateBlock(id, data);
            await refreshTimeBlocks();
          } catch (error) {
            console.error("Error updating time block:", error);
          }
        }}
        onDeleteBlock={async (block) => {
          try {
            if (!block || !block.id || block.id === "undefined") {
              toast.error("Cannot delete time block: Invalid ID");
              return;
            }

            const success = await onDeleteBlock(block.id);
            if (success) {
              // Force refresh to ensure both grid and todo views are updated
              await refreshTimeBlocks();
            }
          } catch (error) {
            console.error("Error deleting time block:", error);
          }
        }}
      />
    </Card>
  );
}
